from setuptools import setup, find_packages

setup(
    name="agentickit",                       # 项目名称
    version="0.1.0",                         # 版本号
    author="bpaas",                      # 作者
    description="Radnova agent开发套件",       # 简要描述
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    packages=find_packages(),                # 自动发现项目中的包
    install_requires=[                       # 依赖项
      "a2a-sdk>=0.2.7,<0.3.0",
      "click>=8.1.8",
      "httpx>=0.28.1",
      "langgraph>=0.4.8",
      "pydantic>=2.10.6",
      "python-dotenv>=1.1.0",
      "uvicorn>=0.34.2",
      "fastapi>=0.115.12",
      "langchain-openai>=0.3.22",
      "toml>=0.10.2",
      "langgraph-checkpoint-postgres>=2.0.21",
      "psycopg>=3.2.9",
      "psycopg-binary>=3.1.9",
      "psycopg-pool>=3.1.9",
      "langchain-postgres>=0.0.14",
      "langfuse>=2.60.8",
      "langchain>=0.3.25",
      "nacos-sdk-python>=2.0.6",
      "langgraph-checkpoint-sqlite>=2.0.10",
      "langchain-mcp-adapters>=0.1.7",
      "mcp>=1.9.3",
    ],
    classifiers=[                            # 分类器，便于 PyPI 管理和检索
      "Programming Language :: Python :: 3",
      "License :: OSI Approved :: MIT License",
      "Operating System :: OS Independent",
    ],
    python_requires='==3.12',                 # Python 版本要求
)