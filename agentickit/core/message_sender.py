from a2a.types import Part, DataPart, TaskState
from a2a.utils import new_agent_text_message

from agentickit.core.context.context_manager import context_saver
from agentickit.core.enums import MessageTag, PartType


async def send_tag_message(context_id:str, tag:MessageTag, part_type:PartType):
  '''
  发送标识信息
  :param part_type:
  :param context_id: A2A上下文id
  :param tag: 标识标签
  :return:
  '''
  context = context_saver.load(context_id)
  if context:
    updater = context['task_updater']
    if updater:
      if tag != MessageTag.MESSAGE_END:
        await updater.add_artifact(
            [Part(root=DataPart(data={'type':tag.value, 'partType':part_type.value}))],
        )
      else:
        await updater.complete()

async def send_message(context_id:str, message: str):
  '''
  发送一条text信息
  :param context_id: A2A上下文id
  :param message: 信息内容
  :return:
  '''
  context = context_saver.load(context_id)
  if context:
    updater = context['task_updater']
    if updater:
      await send_tag_message(context_id, MessageTag.PART_START, PartType.TEXT)
      await updater.update_status(TaskState.working, new_agent_text_message(
          message,
          context['context_id'],
          context['task_id'],
      ),)
      await send_tag_message(context_id, MessageTag.PART_END, PartType.TEXT)


async def send_artifact(context_id: str, message: dict):
  context = context_saver.load(context_id)
  if context:
    updater = context['task_updater']
    if updater:
      await send_tag_message(context_id, MessageTag.PART_START, PartType.DATA)
      await updater.add_artifact(
        [Part(root=DataPart(data=message))],
      )
      await send_tag_message(context_id, MessageTag.PART_END, PartType.DATA)
