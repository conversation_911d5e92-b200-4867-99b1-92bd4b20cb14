from enum import Enum

class MemoryType(Enum):
  MEMORY = "memory"
  SQLITE = "sqlite"
  POSTGRES = "postgres"


class NodeStatus(Enum):
  RUNNING = "running"
  SUCCESS = "success"
  FAILED = "failed"
  INTERRUPT = "interrupt"

class MessageTag(Enum):
  MESSAGE_START = "MESSAGE_START"
  PART_START = "PART_START"
  PART_CONTENT = "PART_CONTENT"
  PART_END = "PART_END"
  MESSAGE_END = "MESSAGE_END"

class PartType(Enum):
  TEXT = "TEXT"
  DATA = "DATA"
  FILE = "FILE"