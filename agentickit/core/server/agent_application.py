import httpx
from a2a.server.apps import A2AFastAPIApplication
from a2a.server.request_handlers import DefaultR<PERSON><PERSON><PERSON><PERSON><PERSON>
from a2a.server.agent_execution import AgentExecutor
from a2a.server.tasks import InMemoryPushNotifier, InMemoryTaskStore

from agentickit.core.server.agent_call_context_builder import AgentCallContextBuilder, \
  AgentRequestContextBuilder
from agentickit.utils.config_util import config


class AgentApplication:
  '''
  agent服务应用类
  '''

  def create_fast_api_application(self, agent_executor:AgentExecutor):
    '''
    创建一个基于fast api的A2A应用
    :return:
    '''
    httpx_client = httpx.AsyncClient()
    context_build = AgentCallContextBuilder()
    request_build = AgentRequestContextBuilder()
    request_handler = DefaultRequestHandler(
        agent_executor=agent_executor,
        task_store=InMemoryTaskStore(),
        push_notifier=InMemoryPushNotifier(httpx_client),
        request_context_builder = request_build,
    )
    card = config.get_agent_card()
    app = A2AFastAPIApplication(
        agent_card=card, http_handler=request_handler, context_builder = context_build
    )
    return app