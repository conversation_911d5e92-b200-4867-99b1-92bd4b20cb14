from context_manager import AgentContext
from context_saver import ContextSaver


class MemoryContextSaver(ContextSaver):
  context_cache: dict = {}

  def contains(self, context_id:str):
    """
    是否存在context_id对应上下文信息
    :param context_id:
    :return:
    """
    return context_id in self.context_cache

  def save(self, context_id:str, context: AgentContext)-> None:
    """
    保存上下文信息
    context_id:上下文id
    context:上下文信息
    """
    self.context_cache[context_id]=context

  def load(self, context_id:str)-> AgentContext:
    """
    读取上下文信息
    context_id:上下文id
    """
    return self.context_cache[context_id] if context_id in self.context_cache else None