from typing import TypedDict, Any

from a2a.server.tasks import TaskUpdater

from agentickit.core.enums import NodeStatus, ContextStatus


class AgentNode(TypedDict):
  node_name: str
  status: NodeStatus
  param: Any
  failed_message: str


class AgentContext(TypedDict):
  # A2A context id
  context_id: str
  # A2A task id
  task_id: str
  # 所使用模型名称
  model_name:str
  # 模型网关地址
  openai_api_base: str
  # 当前执行的节点
  current_node: AgentNode
  # 历史执行节点信息
  node_history: list
  status:ContextStatus
  request_header:dict
  task_updater: TaskUpdater

class ContextState(TypedDict):
  """
  上下文id
  """
  context_id: str

