from typing import Literal
from dataclasses import dataclass, field

@dataclass
class McpAddress:
    url: str
     # 工具token
    tool_token: str
    # mcp协议
    transport: Literal["stdio", "sse", "streamable_http"] = "streamable_http"

@dataclass
class McpConfig:
    # 使用mcp的uid
    use_tool: list[str] = field(default_factory=list)
    # mcp地址
    mcp: dict[str, McpAddress] = field(default_factory=dict)