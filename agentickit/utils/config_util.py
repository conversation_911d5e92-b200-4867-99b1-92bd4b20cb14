import os
from pathlib import Path
from typing import TypedDict

import nacos
import toml
from a2a.types import (
    Agent<PERSON>apa<PERSON>,
    AgentCard,
    AgentSkill,
)

from agentickit.core.enums import MemoryType

DEFAULT_MEMORY_TYPE = 'memory'

class SystemConfig(TypedDict):
    """
    系统配置类
    """
    # 应用域名
    domain: str
    # 端口
    port: int
    # 模型网关
    open_api_base: str
    # 模型key
    open_api_key: str

class AgentCardConfig(TypedDict):
    name: str
    description: str
    url: str
    version: str
    defaultInputModes: list[str]
    defaultOutputModes: list[str]
    capabilities: dict
    skills: list[dict]

class MemoryConfig(TypedDict):
    """
    memory配置类
    """
    type: str
    #type为sqlite时使用
    path: str
    #type为postgres时使用
    host: str
    port: int
    user: str
    password: str
    database: str
    min_pool_size: int
    max_pool_size: int
    timeout_seconds: float


class NacosConfig(TypedDict):
    """
    nacos配置类
    """
    server_addr: str
    namespace: str
    group: str
    username: str
    password: str
    data_id: str

class LangFuse(TypedDict):
    public_key: str
    secret_key: str
    host: str

class LogConfig(TypedDict):
    level: str


def get_log_config() -> LogConfig:
    result = LogConfig(level=get_config("log", "level", "DEBUG"))
    return result

def get_nacos_config():
    nacos_enabled = os.getenv("nacos.config.enable")
    if(not nacos_enabled or nacos_enabled == "false"):
        return None
    result = NacosConfig()
    result["server_addr"] = os.getenv('nacos.config.url')
    result["namespace"] = os.getenv('nacos.config.namespace')
    result["group"] = os.getenv('nacos.config.group')
    result["username"] = os.getenv('nacos.rw.username')
    result["password"] = os.getenv('nacos.rw.password')
    result["data_id"] = os.getenv('agent_name')+'.toml'
    return result

def _load_config(path: str = "application.toml") -> dict:
    if not os.path.exists(path):
        # 当前文件路径
        path = find_application_path(path)
        if not os.path.exists(path):
            raise FileNotFoundError(f"配置文件不存在: {path}")
    return toml.load(path)

def find_application_path(file_name:str, start_path: Path = None) ->str:
    if start_path is None:
        start_path = Path(__file__).parent
    current_path = start_path
    while current_path != current_path.root:
        # 检查当前目录是否存在目标文件
        if (current_path / file_name).exists():
            return str(current_path / file_name)
        # 向上一级目录移动
        current_path = current_path.parent
    return None


def update_nacos_config(raw_config: str):
    global config
    config = toml.loads(raw_config)

def _init_nacos_client(nacos_config: NacosConfig):
    client = nacos.NacosClient(nacos_config["server_addr"], namespace=nacos_config["namespace"], username=nacos_config["username"], password=nacos_config["password"])
    # client.add_config_watcher(
    #     data_id=nacos_config["data_id"],
    #     group=nacos_config["group"],
    #     cb=update_nacos_config,
    # )
    return client

nacos_config = get_nacos_config()
nacos_client = _init_nacos_client(nacos_config) if nacos_config else None

def _load_nacos_config(nacos_config: NacosConfig):
    raw_config = nacos_client.get_config(nacos_config["data_id"], nacos_config["group"])
    return toml.loads(raw_config)

config = _load_nacos_config(nacos_config) if nacos_config else _load_config()


def get_config(group: str, name: str, default: str = None) -> str:
    """
    获取配置
    :param group: 配置组名
    :param name:  配置名称
    :param default: 默认值
    :return: 对应的配置值
    """
    result = default
    keys = group.split(".")
    group_config = config
    for key in keys:
        group_config = group_config[key]
        if(not group_config):
            return default
    if(group_config[name]):
        result = group_config[name]
    return result


def get_memory_config() -> MemoryConfig:
    """
    获取memory配置
    :return: memory配置信息
    """
    result = MemoryConfig()
    result["type"] = get_config("memory", "type", DEFAULT_MEMORY_TYPE)
    if(result["type"] == MemoryType.SQLITE.value):
        result["path"] = get_config("memory.sqlite", "path")
    elif(result["type"] == MemoryType.POSTGRES.value):
        result["host"] = get_config("memory.postgres", "host")
        result["port"] = int(get_config("memory.postgres", "port"))
        result["user"] = get_config("memory.postgres", "user")
        result["password"] = get_config("memory.postgres", "password")
        result["database"] = get_config("memory.postgres", "database")
        result["min_pool_size"] = int(get_config("memory.postgres", "min_pool_size", "2"))
        result["max_pool_size"] = int(get_config("memory.postgres", "max_pool_size", "20"))
        result["timeout_seconds"] = float(get_config("memory.postgres", "timeout_seconds", "30.0"))
    return result


def get_system_config() -> SystemConfig:
    """
    获取系统配置
    :return: 系统配置信息
    """
    result = SystemConfig()
    result["domain"] = get_config("system", "domain", "localhost")
    result["port"] = int(get_config("system", "port", 1000))
    result["open_api_base"] = get_config("system", "open_api_base")
    result["open_api_key"] = get_config("system", "open_api_key")
    return result

def get_lang_fuse_config() -> LangFuse:
    result = LangFuse()
    result["public_key"] = get_config("langfuse", "public_key")
    result["secret_key"] = get_config("langfuse", "secret_key")
    result["host"] = get_config("langfuse", "host")
    return result

def get_agent_card_config() -> AgentCardConfig:
    return config['agent_card']

def get_agent_card() -> AgentCard:
    agent_card_config = get_agent_card_config()
    capabilities = AgentCapabilities(**agent_card_config["capabilities"])
    skills = [AgentSkill(**skill) for skill in agent_card_config["skills"]]
    return AgentCard(
        name=agent_card_config["name"],
        description=agent_card_config["description"],
        url=agent_card_config["url"],
        version=agent_card_config["version"],
        defaultInputModes=agent_card_config["defaultInputModes"],
        defaultOutputModes=agent_card_config["defaultOutputModes"],
        capabilities=capabilities,
        skills=skills,
    )