import asyncio

from agentickit.utils.log_util import log
import sys

def hand_exception(value, traceback):
  log.warn(f'未捕获异常:value={value}, traceback={traceback}')

def hand_async_exception(loop,context):
  # 异步异常处理器
  if "exception" in context:
    log.error("ASYNC_ERROR", context) # 记录异步错误
  else:
    log.warning(f"异步警告: context={context}")

def catch_exception():
  sys.excepthook = hand_exception
  loop = asyncio.get_event_loop()
  loop.set_exception_handler(hand_async_exception)