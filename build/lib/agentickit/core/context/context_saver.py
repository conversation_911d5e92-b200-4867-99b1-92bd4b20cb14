from abc import abstractmethod

from agent_context import AgentContext


class ContextSaver:
  '''
  agent上下文存储器
  '''
  @abstractmethod
  def contains(self, context_id:str):
    """
    是否存在context_id对应上下文信息
    :param context_id:
    :return:
    """

  @abstractmethod
  def save(self, context_id:str, context: AgentContext)-> None:
      """
      保存上下文信息
      context_id:上下文id
      context:上下文信息
      """

  @abstractmethod
  def load(self, context_id:str)-> AgentContext:
    """
    读取上下文信息
    context_id:上下文id
    """
