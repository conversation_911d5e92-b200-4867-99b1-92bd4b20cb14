import asyncio
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_mcp_adapters.tools import NonTextContent, _convert_call_tool_result, _list_all_tools
from langchain_mcp_adapters.sessions import Connection, create_session
from langchain_core.tools import BaseTool, StructuredTool
from langchain_core.runnables import RunnableConfig
from agentickit.core.context.context_manager import context_saver
from typing import Any, cast
from mcp import ClientSession
from mcp.types import (
    Tool as MCPTool,
)


def convert_mcp_tool_to_langchain_tool(
    session: ClientSession | None,
    tool: MCPTool,
    *,
    connection: Connection | None = None,
) -> BaseTool:
    if session is None and connection is None:
        raise ValueError("Either a session or a connection config must be provided")

    async def call_tool(
        config: RunnableConfig,
        **arguments: dict[str, Any]
    ) -> tuple[str | list[str], list[NonTextContent] | None]:
        # 扩展逻辑，获取Header
        token = ""
        if config and "metadata" in config:
            context_id = config["metadata"].get("context_id")
            if context_id:
                context = context_saver.load(context_id)
                if context and "request_header" in context:
                    token = context["request_header"].get('token', '')
        
        if session is None:
            # 如果connection存在，更新其headers
            if connection and hasattr(connection, 'headers'):
                connection.headers.setdefault("token", f"Bearer {token}")
            # If a session is not provided, we will create one on the fly
            async with create_session(connection) as tool_session:
                await tool_session.initialize()
                call_tool_result = await cast(ClientSession, tool_session).call_tool(
                    tool.name, arguments
                )
        else:
            # 对于已存在的session，我们需要通过metadata传递headers
            # 注意：ClientSession本身不支持直接设置headers
            # 我们需要在创建session时就设置好headers
            await cast(ClientSession, tool_session)
            call_tool_result = await session.call_tool(tool.name, arguments)
        return _convert_call_tool_result(call_tool_result)

    return StructuredTool(
        name=tool.name,
        description=tool.description or "",
        args_schema=tool.inputSchema,
        coroutine=call_tool,
        response_format="content_and_artifact",
        metadata=tool.annotations.model_dump() if tool.annotations else None,
    )

async def load_mcp_tools(
    session: ClientSession | None,
    *,
    connection: Connection | None = None,
) -> list[BaseTool]:
    """Load all available MCP tools and convert them to LangChain tools.

    Returns:
        list of LangChain tools. Tool annotations are returned as part
        of the tool metadata object.
    """
    if session is None and connection is None:
        raise ValueError("Either a session or a connection config must be provided")

    if session is None:
        # If a session is not provided, we will create one on the fly
        async with create_session(connection) as tool_session:
            await tool_session.initialize()
            tools = await _list_all_tools(tool_session)
    else:
        tools = await _list_all_tools(session)

    converted_tools = [
        convert_mcp_tool_to_langchain_tool(session, tool, connection=connection) for tool in tools
    ]
    return converted_tools


class MultiServerMCPClientExt(MultiServerMCPClient):
    async def get_tools(self, *, server_name: str | None = None) -> list[BaseTool]:
        """Get a list of all tools from all connected servers.

        Args:
            server_name: Optional name of the server to get tools from.
                If None, all tools from all servers will be returned (default).

        NOTE: a new session will be created for each tool call

        Returns:
            A list of LangChain tools
        """
        if server_name is not None:
            if server_name not in self.connections:
                raise ValueError(
                    f"Couldn't find a server with name '{server_name}', expected one of '{list(self.connections.keys())}'"
                )
            return await load_mcp_tools(None, connection=self.connections[server_name])

        all_tools: list[BaseTool] = []
        load_mcp_tool_tasks = []
        for connection in self.connections.values():
            load_mcp_tool_task = asyncio.create_task(load_mcp_tools(None, connection=connection))
            load_mcp_tool_tasks.append(load_mcp_tool_task)
        tools_list = await asyncio.gather(*load_mcp_tool_tasks)
        for tools in tools_list:
            all_tools.extend(tools)
        return all_tools

