import asyncio
from langgraph.prebuilt import ToolNode
from pydantic import BaseModel
from langchain_core.runnables import RunnableConfig
from langgraph.store.base import BaseStore
from langchain_core.messages import (
    AnyMessage,
)

from typing import (
    Any,
    Optional,
    Union,
)
class MCPToolNode(ToolNode):
    async def _afunc(
        self,
        input: Union[
            list[AnyMessage],
            dict[str, Any],
            BaseModel,
        ],
        config: RunnableConfig,
        *,
        store: Optional[BaseStore],
    ) -> Any:
        tool_calls, input_type = self._parse_input(input, store)
        # 或者使用 setdefault
        if "metadata" not in config:
            config["metadata"] = {}
        config["metadata"]["context_id"] = input.get("context_id")
        outputs = await asyncio.gather(
            *(self._arun_one(call, input_type, config) for call in tool_calls)
        )

        return self._combine_tool_outputs(outputs, input_type)