from a2a.server.agent_execution import SimpleRequest<PERSON>ontextBuilder
from a2a.server.apps.jsonrpc.jsonrpc_app import DefaultCallContextBuilder
from a2a.types import MessageSendParams, Task
from starlette.requests import Request
from a2a.server.context import <PERSON><PERSON>all<PERSON>ontext

from agent_server_call_context import Agent<PERSON><PERSON>r<PERSON>all<PERSON>ontext, \
  AgentRequestContext


class AgentCallContextBuilder(DefaultCallContextBuilder):
  def build(self, request: Request) -> ServerCallContext:
    result = super().build(request)
    context:AgentServerCallContext = AgentServerCallContext()
    context.__dict__.update(result.__dict__)
    context.request = request
    return context


class AgentRequestContextBuilder(SimpleRequestContextBuilder):
  async def build(
      self,
      params: MessageSendParams | None = None,
      task_id: str | None = None,
      context_id: str | None = None,
      task: Task | None = None,
      context: ServerCallContext | None = None,
  ) -> AgentRequestContext:
    result = await super().build(params, task_id, context_id, task, context)
    request_context:AgentRequestContext = AgentRequestContext()
    request_context.__dict__.update(result.__dict__)
    request_context.request = context.request
    return request_context