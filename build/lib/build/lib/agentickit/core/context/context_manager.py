
from agent_context import <PERSON><PERSON>ontex<PERSON>
from agentickit.core.context.context_status import Con<PERSON><PERSON>tat<PERSON>
from memory_context_saver import <PERSON><PERSON>ontextSaver

context_saver:MemoryContextSaver = MemoryContextSaver()



def build_context(slf, task, context, task_updater)->AgentContext:
  if context_saver.contains(task.contextId):
    result = context_saver.load(task.contextId)
    result['request_header'] = dict(context.request.headers)
    result['task_updater'] = task_updater
    return result
  else:
    model_name = slf.agent.model.model_name
    openai_api_base = slf.agent.model.openai_api_base
    ctx = AgentContext(context_id=task.contextId,
                       task_id=task.id,
                       model_name = model_name,
                       openai_api_base = openai_api_base,
                       status=ContextStatus.RUNNING,
                       node_history = [],
                       request_header = dict(context.request.headers),
                       task_updater = task_updater)
    context_saver.save(task.contextId, ctx)
    return ctx

def get_context(context_id:str)->AgentContext:
  return context_saver.load(context_id)