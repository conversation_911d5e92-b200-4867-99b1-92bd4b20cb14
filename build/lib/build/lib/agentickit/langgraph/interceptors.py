from langgraph.errors import GraphInterrupt

from agentickit.core.context.context_manager import context_saver
from agentickit.core.context.context_status import ContextStatus
from agentickit.core.enums import NodeStatus
from agentickit.utils.log_util import log
import copy



def async_agent_node(func):
  """
  节点装饰器
  :param func:
  :return:
  """
  async def wrapper(*args, **kwargs):
    set_cur_node_start(args[0], func)
    try:
      result = await func(*args, **kwargs)
      set_cur_node_end(args[0], func)
      return result
    except Exception as e:
      set_cur_node_failed(args[0], func, e)
      return None

  return wrapper

def async_stream_agent_node(func):
  """
  节点装饰器
  :param func:
  :return:
  """
  async def wrapper(*args, **kwargs):
    set_cur_node_start(args[0], func)
    try:
      async for item in func(*args, **kwargs):
        yield item
      set_cur_node_end(args[0], func)
    except Exception as e:
      set_cur_node_failed(args[0], func, e)
    print('end')
  return wrapper

def agent_node(func):
  """
  节点装饰器
  :param func:
  :return:
  """
  def wrapper(*args, **kwargs):
    print('start')
    set_cur_node_start(args[0], func)
    try:
      result = func(*args, **kwargs)
      set_cur_node_end(args[0], func)
      return result
    except Exception as e:
      set_cur_node_failed(args[0], func, e)
    print('end')
    return None
  return wrapper


def set_cur_node_start(state, func):
  if 'context_id' not in state:
    return
  context = context_saver.load(state['context_id'])
  if not context:
    log.info(f"context not found. context_id:{state['context_id']}")
    return
  if 'current_node' not in context:
    context['current_node'] = {}
  context['current_node']['node_name'] = func.__name__
  context['current_node']['status'] = NodeStatus.RUNNING.value
  context['status'] = ContextStatus.RUNNING
  if 'messages' in state:
    context['current_node']['param'] = state['messages'][-1]
  else:
    deepcopy = copy.deepcopy(state)
    if 'context' in deepcopy:
      deepcopy.pop('context', None)
    context['current_node']['param'] = deepcopy
  if 'node_history' not in context:
    context['node_history'] = []
  context['node_history'].append(copy.deepcopy(context['current_node']))
  context_saver.save(state['context_id'], context)

def set_cur_node_end(state, func):
  if 'context_id' not in state:
    return
  context = context_saver.load(state['context_id'])
  if not context:
    log.info(f"context not found. context_id:{state['context_id']}")
    return
  cur_node = next((item for item in context['node_history'] if item['node_name'] == func.__name__), None)
  if cur_node:
    cur_node['status'] = NodeStatus.SUCCESS.value
  context_saver.save(state['context_id'], context)

def set_cur_node_failed(state, func, e):
  if 'context_id' not in state:
    return
  context = context_saver.load(state['context_id'])
  if not context:
    log.info(f"context not found. context_id:{state['context_id']}")
    return
  cur_node = next((item for item in context['node_history'] if item['node_name'] == func.__name__), None)
  if cur_node:
    if isinstance(e, GraphInterrupt):
      cur_node['status'] = NodeStatus.INTERRUPT.value
      context['status'] = ContextStatus.INTERRUPT
      context_saver.save(state['context_id'], context)
      raise e
    else:
      cur_node['status'] = NodeStatus.FAILED.value
      cur_node['failed_message'] = str(e)
      context_saver.save(state['context_id'], context)
