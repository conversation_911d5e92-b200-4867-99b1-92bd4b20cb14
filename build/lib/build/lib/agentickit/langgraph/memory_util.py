"""
内存保存器工具模块

该模块提供了不同类型的内存保存器实现，用于LangGraph的检查点功能。
支持内存、SQLite和PostgreSQL三种存储方式。
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator, Any, Optional

from langgraph.checkpoint.memory import MemorySaver
from langgraph.checkpoint.postgres import PostgresSaver
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from psycopg import AsyncConnection
from psycopg.rows import dict_row
from psycopg_pool import AsyncConnectionPool

from agentickit.utils.config_util import get_memory_config
from agentickit.utils.log_util import log

# 获取配置对象
memory_config = get_memory_config()

# 全局连接池实例
_global_postgres_pool: Optional[AsyncConnectionPool] = None
_pool_lock = asyncio.Lock()


def memory_saver():
    """
    根据配置获取相应的内存保存器实例

    该函数根据配置文件中的memory.type设置，返回对应的内存保存器：
    - memory: 使用内存存储（默认）
    - sqlite: 使用SQLite数据库存储
    - postgres: 使用PostgreSQL数据库存储

    Returns:
        MemorySaver | SqliteSaver | PostgresSaver: 对应类型的内存保存器实例

    Raises:
        ValueError: 当配置的memory类型不受支持时抛出异常
    """
    # 从配置中获取内存相关配置，如果没有则使用空字典
    # 获取内存类型，默认为"memory"
    memory_type = memory_config.get("type", "memory")
    log.info(f"memory_type is {memory_type}")

    # 根据配置的内存类型返回相应的保存器实例
    if memory_type == "memory":
        # 使用内存存储，数据仅在程序运行期间保存
        return MemorySaver()

    elif memory_type == "sqlite":
        # 使用SQLite数据库存储
        # 获取数据库文件路径，默认为"memory.db"
        db_path = memory_config.get("path", "memory.db")
        log.info(f"sqlite path is {db_path}")
        from langgraph.checkpoint.sqlite import SqliteSaver
        import os
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        return SqliteSaver.from_conn_string(db_path)

    elif memory_type == "postgres":
        # PostgreSQL的同步版本需要上下文管理器，暂时使用内存存储作为后备
        log.warning("PostgreSQL同步模式暂不支持，使用内存存储作为后备")
        return PostgresSaver.from_conn_string(_build_postgres_conn_string())

    else:
        # 抛出异常，表示不支持的内存类型
        raise ValueError(f"不支持的 memory 类型: {memory_type}")


def async_memory_saver():
    """
    异步获取内存保存器实例，正确处理需要上下文管理器的情况

    该函数根据配置文件中的memory.type设置，返回对应的内存保存器：
    - memory: 使用内存存储（默认）
    - sqlite: 使用SQLite数据库存储
    - postgres: 使用PostgreSQL数据库存储

    Returns:
        MemorySaver | AsyncSqliteSaver | PostgresSaver: 对应类型的内存保存器实例

    Raises:
        ValueError: 当配置的memory类型不受支持时抛出异常
    """
    # 从配置中获取内存相关配置，如果没有则使用空字典
    # 获取内存类型，默认为"memory"
    memory_type = memory_config.get("type", "memory")
    log.debug(f"async memory_type is {memory_type}")

    # 根据配置的内存类型返回相应的保存器实例
    if memory_type == "memory":
        # 使用内存存储，数据仅在程序运行期间保存
        return MemorySaver()

    elif memory_type == "sqlite":
        # 使用SQLite数据库存储
        # 获取数据库文件路径，默认为"memory.db"
        db_path = memory_config.get("path", "memory.db")
        log.info(f"async sqlite path is {db_path}")
        from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
        import os
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        return AsyncSqliteSaver.from_conn_string(db_path)

    elif memory_type == "postgres":
        # 使用PostgreSQL数据库存储 - 异步版本
        # 注意：PostgreSQL需要使用异步上下文管理器
        return _async_postgres_memory_saver()

    else:
        # 抛出异常，表示不支持的内存类型
        raise ValueError(f"不支持的 memory 类型: {memory_type}")


async def _check_connection(conn: AsyncConnection):
    await conn.execute("SELECT 1")


async def _get_global_postgres_pool() -> AsyncConnectionPool:
    """
    获取全局PostgreSQL连接池实例（单例模式）

    如果连接池不存在，则创建一个新的连接池。
    连接池在应用启动时创建一次，然后在整个应用生命周期中复用。

    Returns:
        AsyncConnectionPool: 全局PostgreSQL连接池实例
    """
    global _global_postgres_pool

    async with _pool_lock:
        if _global_postgres_pool is None:
            # 构建连接字符串
            conn_string = _build_postgres_conn_string()
            log.info(f"创建全局PostgreSQL连接池")

            # 创建连接池
            _global_postgres_pool = AsyncConnectionPool(
                conninfo=conn_string,
                max_size=memory_config.get("max_pool_size", 10),  # 最大连接数
                min_size=memory_config.get("min_pool_size", 2),  # 最小连接数
                timeout=memory_config.get("timeout_seconds", 30.0),  # 连接超时时间
                kwargs={
                    "autocommit": True,
                    "prepare_threshold": 0,
                    "row_factory": dict_row,
                },
                check=_check_connection,
                open=False,
            )

            # 打开连接池
            await _global_postgres_pool.open()
            log.info("全局PostgreSQL连接池已创建并打开")

        return _global_postgres_pool


@asynccontextmanager
async def _get_postgres_connection():
    """
    从全局连接池获取PostgreSQL连接的异步上下文管理器

    Yields:
        PostgreSQL连接实例

    Example:
        async with get_postgres_connection() as conn:
            # 使用连接
            pass
    """
    pool = await _get_global_postgres_pool()
    async with pool.connection() as conn:
        yield conn


@asynccontextmanager
async def _async_postgres_memory_saver() -> AsyncGenerator[Any, None]:
    """
    异步PostgreSQL内存保存器上下文管理器

    使用全局连接池获取连接，避免重复创建连接池

    Yields:
        PostgreSQL保存器实例

    Example:
        async with async_postgres_memory_saver() as checkpointer:
            graph = graph.compile(checkpointer=checkpointer)
    """
    # 使用全局连接池获取连接
    async with _get_postgres_connection() as conn:
        checkpointer = AsyncPostgresSaver(conn)
        await checkpointer.setup()
        log.debug("PostgreSQL保存器已创建（使用全局连接池）")
        try:
            yield checkpointer
        finally:
            log.debug("PostgreSQL保存器已释放")


def _build_postgres_conn_string():
    """
    构建PostgreSQL连接字符串

    Returns:
        str: PostgreSQL连接字符串
    """
    return (f"postgres://{memory_config.get('user')}:{memory_config.get('password')}"
            f"@{memory_config.get('host')}:{memory_config.get('port')}/"
            f"{memory_config.get('database')}?sslmode=disable")
