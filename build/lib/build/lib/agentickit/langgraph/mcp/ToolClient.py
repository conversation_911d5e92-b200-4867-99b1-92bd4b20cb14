import asyncio
from langchain_core.tools import BaseTool, tool as create_tool
from typing import Union, Callable, Any, Optional
from tool import MultiServerMCPClientExt

from model import McpConfig

class ToolClient:
    useTool: list[str] = []
    client: MultiServerMCPClientExt = []
    tool_all: list[Union[BaseTool, Callable]] = []
    def __init__(self, config: McpConfig):
        mcp_config = {}
        # 封装mcp地址
        for name in config.mcp:
            mcp = config.mcp[name]
            mcp_config[name] = {
                "url": mcp.url,
                "transport": mcp.transport,
                "headers": {"token":mcp.tool_token}
            }
        # 声明多MCP客户端
        self.client = MultiServerMCPClientExt(mcp_config)
        self.useTool = config.use_tool
        
    async def init_tools(self):
        '''从mcp中获取tool并进行过滤'''
        self.tool_all = []
            # 获取tool
        tools = await self.client.get_tools()
        print(f"可用的 MCP 工具：{[tool.name for tool in tools]}")
        if self.useTool and len(self.useTool) > 0:
            # 添加需要使用的mcp tool
            for tool in tools:
                if tool.name in self.useTool: # 过滤
                    self.tool_all.append(tool)
        else :
            for tool in tools:
                self.tool_all.append(tool)
        return self.tool_all
    
    def get_tools(self):
        '''获取工具类'''
        return self.tool_all
    
    def get_client(self):
        return self.client
    
    def get_use_tool(self):
        return self.useTool
    
    def tool(self, name: Optional[str] = None, description: Optional[str] = None):
        def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
            tool = create_tool(func)
            # 将函数添加到工具列表
            self.tool_all.append(tool)
            return tool
        return decorator
    

def createToolClient(config: McpConfig) -> ToolClient:
    client = ToolClient(config)
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        loop.run_until_complete(client.init_tools())
        return client
    finally:
        loop.close()