import contextvars
import logging
import os
from logging.handlers import TimedRotatingFileHandler

from agentickit.utils.config_util import get_log_config

# 当前 trace_id 上下文变量（线程/协程独立）
_trace_id_var = contextvars.ContextVar("trace_id", default="NA")


# 设置 trace_id
def _set_trace_id(tid: str):
    _trace_id_var.set(tid)


# 获取 trace_id
def _get_trace_id():
    return _trace_id_var.get()


# 日志格式化器，支持 trace_id
class TraceFormatter(logging.Formatter):
    def format(self, record):
        record.trace_id = _get_trace_id()
        return super().format(record)


def _setup_logger(name='app.log', log_dir='logs', log_level="DEBUG", enable_console=True):
    os.makedirs(log_dir, exist_ok=True)

    _logger = logging.getLogger(name)

    log_level = log_level.upper() if log_level else "DEBUG"

    if log_level == "DEBUG":
        _logger.setLevel(logging.DEBUG)
    elif log_level == "INFO":
        _logger.setLevel(logging.INFO)
    elif log_level == "WARNING":
        _logger.setLevel(logging.WARNING)
    elif log_level == "ERROR":
        _logger.setLevel(logging.ERROR)
    else:
        _logger.setLevel(logging.DEBUG)

    log_file = os.path.join(log_dir, name)
    handler = TimedRotatingFileHandler(log_file, when="midnight", backupCount=7, encoding="utf-8")

    formatter = TraceFormatter(
        fmt="[%(asctime)s] [%(threadName)s] %(levelname)s [%(filename)s:%(lineno)d] [%(trace_id)s] - %(message)s"
    )

    handler.setFormatter(formatter)
    _logger.addHandler(handler)
    if enable_console:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        _logger.addHandler(console_handler)
    return _logger


log = _setup_logger(log_level=get_log_config().get("level", "DEBUG"))
